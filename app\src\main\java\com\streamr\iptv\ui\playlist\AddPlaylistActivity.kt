package com.streamr.iptv.ui.playlist

import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.streamr.iptv.R
import com.streamr.iptv.data.model.Playlist
import com.streamr.iptv.data.model.PlaylistType
import com.streamr.iptv.databinding.ActivityAddPlaylistBinding
import kotlinx.coroutines.launch

class AddPlaylistActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityAddPlaylistBinding
    private val viewModel: AddPlaylistViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddPlaylistBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
    }
    
    private fun setupUI() {
        // Back button
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        // Radio button group
        binding.rbXtreamCodes.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.rbM3uPlaylist.isChecked = false
                updateUIForXtreamCodes()
            }
        }
        
        binding.rbM3uPlaylist.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.rbXtreamCodes.isChecked = false
                updateUIForM3U()
            }
        }
        
        // Add playlist button
        binding.btnAddPlaylist.setOnClickListener {
            addPlaylist()
        }
        
        // Initialize with Xtream Codes selected
        updateUIForXtreamCodes()
    }
    
    private fun updateUIForXtreamCodes() {
        binding.etUsername.isEnabled = true
        binding.etPassword.isEnabled = true
        binding.etUsername.alpha = 1.0f
        binding.etPassword.alpha = 1.0f
    }
    
    private fun updateUIForM3U() {
        binding.etUsername.isEnabled = false
        binding.etPassword.isEnabled = false
        binding.etUsername.alpha = 0.5f
        binding.etPassword.alpha = 0.5f
        binding.etUsername.setText("")
        binding.etPassword.setText("")
    }
    
    private fun addPlaylist() {
        val name = binding.etPlaylistName.text.toString().trim()
        val username = binding.etUsername.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()
        val serverUrl = binding.etServerUrl.text.toString().trim()
        val isRemembered = binding.switchRememberMe.isChecked
        val type = if (binding.rbXtreamCodes.isChecked) PlaylistType.XTREAM_CODES else PlaylistType.M3U_PLAYLIST
        
        // Validation
        if (name.isEmpty()) {
            binding.etPlaylistName.error = "Playlist name is required"
            return
        }
        
        if (serverUrl.isEmpty()) {
            binding.etServerUrl.error = "Server URL is required"
            return
        }
        
        if (type == PlaylistType.XTREAM_CODES) {
            if (username.isEmpty()) {
                binding.etUsername.error = "Username is required for Xtream Codes"
                return
            }
            if (password.isEmpty()) {
                binding.etPassword.error = "Password is required for Xtream Codes"
                return
            }
        }
        
        val playlist = Playlist(
            name = name,
            type = type,
            username = if (type == PlaylistType.XTREAM_CODES) username else null,
            password = if (type == PlaylistType.XTREAM_CODES) password else null,
            serverUrl = serverUrl,
            isRemembered = isRemembered
        )
        
        lifecycleScope.launch {
            viewModel.addPlaylist(playlist)
        }
    }
    
    private fun observeViewModel() {
        viewModel.addPlaylistResult.observe(this) { result ->
            result.fold(
                onSuccess = { playlistId ->
                    Toast.makeText(this, "Playlist added successfully", Toast.LENGTH_SHORT).show()
                    finish()
                },
                onFailure = { error ->
                    Toast.makeText(this, "Error: ${error.message}", Toast.LENGTH_LONG).show()
                }
            )
        }
        
        viewModel.isLoading.observe(this) { isLoading ->
            binding.btnAddPlaylist.isEnabled = !isLoading
            binding.btnAddPlaylist.text = if (isLoading) "Adding..." else getString(R.string.add_playlist)
        }
    }
}
