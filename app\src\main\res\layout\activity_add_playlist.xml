<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_primary"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/text_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/add_playlist"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Playlist Name -->
            <EditText
                android:id="@+id/etPlaylistName"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edittext_background"
                android:hint="@string/playlist_name"
                android:inputType="text"
                android:padding="16dp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:textSize="16sp" />

            <!-- Playlist Type Toggle -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edittext_background"
                android:orientation="horizontal"
                android:padding="4dp">

                <RadioButton
                    android:id="@+id/rbXtreamCodes"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/toggle_background"
                    android:button="@null"
                    android:checked="true"
                    android:gravity="center"
                    android:text="@string/xtream_codes"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <RadioButton
                    android:id="@+id/rbM3uPlaylist"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/toggle_background"
                    android:button="@null"
                    android:gravity="center"
                    android:text="@string/m3u_playlist"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Username -->
            <EditText
                android:id="@+id/etUsername"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edittext_background"
                android:hint="@string/username"
                android:inputType="text"
                android:padding="16dp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:textSize="16sp" />

            <!-- Password -->
            <EditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edittext_background"
                android:hint="@string/password"
                android:inputType="textPassword"
                android:padding="16dp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:textSize="16sp" />

            <!-- Server URL -->
            <EditText
                android:id="@+id/etServerUrl"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/edittext_background"
                android:hint="@string/server_url"
                android:inputType="textUri"
                android:padding="16dp"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_hint"
                android:textSize="16sp" />

            <!-- Remember Me -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/remember_me"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <Switch
                    android:id="@+id/switchRememberMe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:thumb="@drawable/switch_thumb"
                    android:track="@drawable/switch_track" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Add Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <Button
            android:id="@+id/btnAddPlaylist"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/button_background"
            android:text="@string/add_playlist"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <View
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/background_primary" />

    </LinearLayout>

</LinearLayout>
