<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Playlists Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Playlists"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPlaylists"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="false" />

        <LinearLayout
            android:id="@+id/emptyPlaylistsState"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="No playlists added"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <Button
            android:id="@+id/btnAddPlaylist"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/button_background"
            android:text="@string/add_playlist"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Settings Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="@string/settings"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/settingPlayerSettings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_play"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/player_settings"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right"
                android:tint="@color/text_secondary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/settingAppSettings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_settings"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/app_settings"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right"
                android:tint="@color/text_secondary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/settingAbout"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_info"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/about"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right"
                android:tint="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
