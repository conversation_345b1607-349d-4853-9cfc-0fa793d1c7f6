package com.streamr.iptv.ui.playlist

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.Playlist
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.launch

class AddPlaylistViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    private val _addPlaylistResult = MutableLiveData<Result<Long>>()
    val addPlaylistResult: LiveData<Result<Long>> = _addPlaylistResult
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    suspend fun addPlaylist(playlist: Playlist) {
        _isLoading.value = true
        
        try {
            val playlistId = repository.insertPlaylist(playlist)
            _addPlaylistResult.value = Result.success(playlistId)
            
            // TODO: Load playlist content (channels, movies, series) from server
            loadPlaylistContent(playlist.copy(id = playlistId))
            
        } catch (e: Exception) {
            _addPlaylistResult.value = Result.failure(e)
        } finally {
            _isLoading.value = false
        }
    }
    
    private suspend fun loadPlaylistContent(playlist: Playlist) {
        viewModelScope.launch {
            try {
                when (playlist.type) {
                    com.streamr.iptv.data.model.PlaylistType.XTREAM_CODES -> {
                        loadXtreamCodesContent(playlist)
                    }
                    com.streamr.iptv.data.model.PlaylistType.M3U_PLAYLIST -> {
                        loadM3UContent(playlist)
                    }
                }
            } catch (e: Exception) {
                // Log error but don't fail the playlist creation
                e.printStackTrace()
            }
        }
    }
    
    private suspend fun loadXtreamCodesContent(playlist: Playlist) {
        // TODO: Implement Xtream Codes API integration
        // This would typically involve:
        // 1. Authenticate with the server
        // 2. Get categories
        // 3. Get live streams
        // 4. Get VOD (movies)
        // 5. Get series
        // 6. Parse and save to database
    }
    
    private suspend fun loadM3UContent(playlist: Playlist) {
        // TODO: Implement M3U playlist parsing
        // This would typically involve:
        // 1. Download M3U file from URL
        // 2. Parse M3U content
        // 3. Extract channel information
        // 4. Save channels to database
    }
}
