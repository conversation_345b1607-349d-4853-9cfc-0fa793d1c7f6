<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_primary"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingStart="48dp"
            android:text="@string/app_name"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/btnSearch"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/ic_search"
            app:tint="@color/text_primary" />

    </LinearLayout>

    <!-- Content -->
    <FrameLayout
        android:id="@+id/fragmentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Bottom Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_secondary"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/primary_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingTop="8dp"
            android:paddingBottom="12dp">

            <LinearLayout
                android:id="@+id/navHome"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="8dp">

                <ImageView
                    android:id="@+id/iconHome"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_home_filled"
                    app:tint="@color/text_primary" />

                <TextView
                    android:id="@+id/textHome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/nav_home"
                    android:textColor="@color/text_primary"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navLiveTV"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="8dp">

                <ImageView
                    android:id="@+id/iconLiveTV"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_tv"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/textLiveTV"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/nav_live_tv"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navMovies"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="8dp">

                <ImageView
                    android:id="@+id/iconMovies"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_movie"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/textMovies"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/nav_movies"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navSeries"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="8dp">

                <ImageView
                    android:id="@+id/iconSeries"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_tv"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/textSeries"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/nav_series"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/navSettings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="8dp">

                <ImageView
                    android:id="@+id/iconSettings"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    app:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/textSettings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="@string/nav_settings"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/background_secondary" />

    </LinearLayout>

</LinearLayout>
