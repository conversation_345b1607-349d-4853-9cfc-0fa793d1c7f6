package com.streamr.iptv.ui.settings

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.streamr.iptv.databinding.FragmentSettingsBinding
import com.streamr.iptv.ui.playlist.AddPlaylistActivity

class SettingsFragment : Fragment() {
    
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: SettingsViewModel by viewModels()
    private lateinit var playlistAdapter: PlaylistAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        playlistAdapter = PlaylistAdapter(
            onEditClick = { playlist ->
                // TODO: Edit playlist
            },
            onDeleteClick = { playlist ->
                viewModel.deletePlaylist(playlist)
            }
        )
        
        binding.rvPlaylists.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = playlistAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.btnAddPlaylist.setOnClickListener {
            startActivity(Intent(requireContext(), AddPlaylistActivity::class.java))
        }
        
        binding.settingPlayerSettings.setOnClickListener {
            // TODO: Open player settings
        }
        
        binding.settingAppSettings.setOnClickListener {
            // TODO: Open app settings
        }
        
        binding.settingAbout.setOnClickListener {
            // TODO: Open about dialog
        }
    }
    
    private fun observeViewModel() {
        viewModel.playlists.observe(viewLifecycleOwner) { playlists ->
            playlistAdapter.submitList(playlists)
            binding.emptyPlaylistsState.visibility = if (playlists.isEmpty()) View.VISIBLE else View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
