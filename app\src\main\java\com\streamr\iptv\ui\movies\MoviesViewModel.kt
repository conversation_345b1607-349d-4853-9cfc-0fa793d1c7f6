package com.streamr.iptv.ui.movies

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem

class MoviesViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val movies: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val playlists = repository.getActivePlaylists()
        addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                val firstPlaylist = playlistList.first()
                removeSource(playlists)
                addSource(repository.getMoviesByPlaylist(firstPlaylist.id)) { movieList ->
                    value = movieList?.map { MediaItem.fromMovie(it) } ?: emptyList()
                }
            } else {
                value = emptyList()
            }
        }
    }
    
    fun playMovie(movieItem: MediaItem) {
        // TODO: Implement movie playback
        // This would typically:
        // 1. Update watch history
        // 2. Start PlayerActivity with the movie
    }
}
