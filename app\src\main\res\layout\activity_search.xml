<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_primary"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/text_primary" />

        <EditText
            android:id="@+id/etSearch"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="@drawable/edittext_background"
            android:hint="@string/search"
            android:inputType="text"
            android:padding="16dp"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_hint"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvSearchResults"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="16dp" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/emptyState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:alpha="0.6"
                android:src="@drawable/ic_search"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="Start typing to search"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Search for channels, movies, and series"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
