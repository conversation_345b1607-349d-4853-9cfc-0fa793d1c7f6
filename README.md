# Streamr IPTV - تطبيق IPTV للأندرويد

تطبيق IPTV متكامل لنظام Android مبني بناءً على التصاميم المقدمة، يدعم تشغيل القنوات المباشرة والأفلام والمسلسلات.

## المميزات

### 🏠 الواجهة الرئيسية
- عرض المحتوى المستمر في المشاهدة
- الأفلام المضافة حديثاً
- المسلسلات الرائجة
- القنوات المميزة المباشرة

### 📺 التلفزيون المباشر
- تصفح القنوات حسب الفئات
- عرض جميع القنوات
- دعم التسجيل (قريباً)
- فئات متنوعة: رياضة، أخبار، ترفيه، أطفال، موسيقى، وثائقيات

### 🎬 الأفلام والمسلسلات
- تصفح مكتبة الأفلام
- عرض المسلسلات مع الحلقات
- تتبع تقدم المشاهدة
- إضافة للمفضلة

### ⚙️ الإعدادات
- إدارة قوائم التشغيل
- إعدادات المشغل
- إعدادات التطبيق

## التقنيات المستخدمة

- **Kotlin** - لغة البرمجة الأساسية
- **Android Architecture Components** - ViewModel, LiveData, Room
- **Room Database** - قاعدة البيانات المحلية
- **ExoPlayer** - مشغل الوسائط
- **Retrofit** - للاتصال بالشبكة
- **Glide** - تحميل وعرض الصور
- **Material Design** - تصميم واجهة المستخدم

## هيكل المشروع

```
app/
├── src/main/java/com/streamr/iptv/
│   ├── data/
│   │   ├── database/          # قاعدة البيانات المحلية
│   │   ├── model/             # نماذج البيانات
│   │   └── repository/        # طبقة البيانات
│   ├── ui/
│   │   ├── adapters/          # محولات RecyclerView
│   │   ├── home/              # الواجهة الرئيسية
│   │   ├── livetv/            # التلفزيون المباشر
│   │   ├── movies/            # الأفلام
│   │   ├── series/            # المسلسلات
│   │   ├── settings/          # الإعدادات
│   │   ├── playlist/          # إدارة قوائم التشغيل
│   │   ├── player/            # مشغل الوسائط
│   │   └── search/            # البحث
│   └── StreamrApplication.kt  # تطبيق Android الأساسي
└── res/
    ├── layout/                # ملفات التخطيط
    ├── drawable/              # الرسوميات والأيقونات
    ├── values/                # الألوان والنصوص والأنماط
    └── ...
```

## أنواع قوائم التشغيل المدعومة

### Xtream Codes API
- دعم كامل لـ Xtream Codes API
- تسجيل الدخول بالمستخدم وكلمة المرور
- تحميل القنوات والأفلام والمسلسلات تلقائياً

### M3U Playlists
- دعم ملفات M3U القياسية
- تحليل معلومات القنوات من ملف M3U
- دعم الشعارات والمجموعات

## قاعدة البيانات

التطبيق يستخدم Room Database لحفظ:
- قوائم التشغيل
- القنوات المباشرة
- الأفلام
- المسلسلات والحلقات
- سجل المشاهدة
- المفضلة

## التشغيل والبناء

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK 21+ (Android 5.0)
- Kotlin 1.9.20+

### خطوات البناء
1. استنساخ المشروع
2. فتح المشروع في Android Studio
3. مزامنة Gradle
4. تشغيل التطبيق على جهاز أو محاكي

## الاستخدام

### إضافة قائمة تشغيل جديدة
1. اذهب إلى الإعدادات
2. اضغط على "إضافة قائمة تشغيل"
3. اختر نوع القائمة (Xtream Codes أو M3U)
4. أدخل المعلومات المطلوبة
5. احفظ القائمة

### مشاهدة المحتوى
- اختر القناة أو الفيلم من القائمة
- سيتم فتح المشغل تلقائياً
- استخدم عناصر التحكم للتشغيل والإيقاف

## المميزات القادمة

- [ ] دعم التسجيل
- [ ] دعم الترجمة
- [ ] مشاركة المحتوى
- [ ] وضع الصورة في الصورة
- [ ] دعم Chromecast
- [ ] إعدادات جودة الفيديو
- [ ] دعم VPN مدمج

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في GitHub.

---

**ملاحظة**: هذا التطبيق مخصص للاستخدام مع المحتوى المرخص قانونياً فقط. المطورون غير مسؤولين عن أي استخدام غير قانوني للتطبيق.
