package com.streamr.iptv.ui.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.*
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem

class HomeViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val continueWatching: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val recentChannels = repository.getRecentlyWatchedChannels()
        val recentMovies = repository.getRecentlyWatchedMovies()
        val recentSeries = repository.getRecentlyWatchedSeries()
        
        addSource(recentChannels) { channels ->
            val combined = mutableListOf<MediaItem>()
            channels?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            recentMovies.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            recentSeries.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
        
        addSource(recentMovies) { movies ->
            val combined = mutableListOf<MediaItem>()
            recentChannels.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            movies?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            recentSeries.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
        
        addSource(recentSeries) { series ->
            val combined = mutableListOf<MediaItem>()
            recentChannels.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            recentMovies.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            series?.let { combined.addAll(it.map { s -> MediaItem.fromSeries(s) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
    }
    
    val recentMovies: LiveData<List<MediaItem>> = repository.getRecentlyAddedMovies().map { movies ->
        movies.map { MediaItem.fromMovie(it) }
    }
    
    val trendingSeries: LiveData<List<MediaItem>> = repository.getRecentlyWatchedSeries().map { series ->
        series.map { MediaItem.fromSeries(it) }
    }
    
    val featuredChannels: LiveData<List<MediaItem>> = repository.getFavoriteChannels().map { channels ->
        channels.map { MediaItem.fromChannel(it) }
    }
    
    val hasPlaylists: LiveData<Boolean> = repository.getActivePlaylists().map { playlists ->
        playlists.isNotEmpty()
    }
    
    fun playMedia(mediaItem: MediaItem) {
        // TODO: Implement media playback
        // This would typically:
        // 1. Update watch history
        // 2. Start PlayerActivity with the media item
        when (mediaItem.type) {
            MediaItem.Type.CHANNEL -> {
                // Play live channel
            }
            MediaItem.Type.MOVIE -> {
                // Play movie
            }
            MediaItem.Type.SERIES -> {
                // Show series episodes
            }
        }
    }
}
