package com.streamr.iptv.data.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize

@Parcelize
@Entity(tableName = "playlists")
data class Playlist(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val type: PlaylistType,
    val username: String? = null,
    val password: String? = null,
    val serverUrl: String,
    val isRemembered: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val lastUsed: Long = System.currentTimeMillis(),
    val isActive: Boolean = true
) : Parcelable

enum class PlaylistType {
    XTREAM_CODES,
    M3U_PLAYLIST
}

@Parcelize
@Entity(tableName = "channels")
data class Channel(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val playlistId: Long,
    val name: String,
    val streamUrl: String,
    val logoUrl: String? = null,
    val groupTitle: String? = null,
    val tvgId: String? = null,
    val tvgName: String? = null,
    val category: ChannelCategory = ChannelCategory.LIVE_TV,
    val isHD: Boolean = false,
    val language: String? = null,
    val country: String? = null,
    val lastWatched: Long? = null,
    val watchCount: Int = 0,
    val isFavorite: Boolean = false
) : Parcelable

enum class ChannelCategory {
    LIVE_TV,
    MOVIES,
    SERIES,
    SPORTS,
    NEWS,
    ENTERTAINMENT,
    KIDS,
    MUSIC,
    DOCUMENTARY,
    OTHER
}

@Parcelize
@Entity(tableName = "movies")
data class Movie(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val playlistId: Long,
    val name: String,
    val streamUrl: String,
    val posterUrl: String? = null,
    val backdropUrl: String? = null,
    val description: String? = null,
    val genre: String? = null,
    val year: Int? = null,
    val rating: Float? = null,
    val duration: Int? = null, // in minutes
    val director: String? = null,
    val cast: String? = null,
    val lastWatched: Long? = null,
    val watchProgress: Long = 0, // in milliseconds
    val isWatched: Boolean = false,
    val isFavorite: Boolean = false,
    val addedAt: Long = System.currentTimeMillis()
) : Parcelable

@Parcelize
@Entity(tableName = "series")
data class Series(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val playlistId: Long,
    val name: String,
    val posterUrl: String? = null,
    val backdropUrl: String? = null,
    val description: String? = null,
    val genre: String? = null,
    val year: Int? = null,
    val rating: Float? = null,
    val totalSeasons: Int = 1,
    val totalEpisodes: Int = 0,
    val director: String? = null,
    val cast: String? = null,
    val lastWatched: Long? = null,
    val isFavorite: Boolean = false,
    val addedAt: Long = System.currentTimeMillis()
) : Parcelable

@Parcelize
@Entity(tableName = "episodes")
data class Episode(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val seriesId: Long,
    val seasonNumber: Int,
    val episodeNumber: Int,
    val name: String,
    val streamUrl: String,
    val thumbnailUrl: String? = null,
    val description: String? = null,
    val duration: Int? = null, // in minutes
    val airDate: String? = null,
    val lastWatched: Long? = null,
    val watchProgress: Long = 0, // in milliseconds
    val isWatched: Boolean = false
) : Parcelable
