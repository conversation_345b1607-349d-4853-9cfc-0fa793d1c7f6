<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="240dp"
    android:layout_height="wrap_content"
    android:layout_marginEnd="12dp"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="135dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivThumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_card"
                android:scaleType="centerCrop" />

            <!-- Progress Bar for Movies/Series -->
            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_gravity="bottom"
                android:layout_marginHorizontal="8dp"
                android:layout_marginBottom="8dp"
                android:progressTint="@color/accent_green"
                android:visibility="gone" />

            <!-- Favorite Icon -->
            <ImageView
                android:id="@+id/ivFavorite"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/favorite_background"
                android:padding="4dp"
                android:src="@drawable/ic_favorite"
                android:visibility="gone"
                app:tint="@color/accent_green" />

            <!-- Live Indicator -->
            <TextView
                android:id="@+id/tvLiveIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:layout_margin="8dp"
                android:background="@drawable/live_indicator_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="LIVE"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="Media Title"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="normal" />

</LinearLayout>
