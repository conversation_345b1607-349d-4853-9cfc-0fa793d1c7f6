package com.streamr.iptv.ui.adapters

import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.data.model.Movie
import com.streamr.iptv.data.model.Series

data class MediaItem(
    val id: Long,
    val title: String,
    val imageUrl: String?,
    val type: Type,
    val streamUrl: String? = null,
    val lastWatched: Long? = null,
    val progress: Long = 0,
    val isFavorite: Boolean = false
) {
    enum class Type {
        CHANNEL,
        MOVIE,
        SERIES
    }
    
    companion object {
        fun fromChannel(channel: Channel): MediaItem {
            return MediaItem(
                id = channel.id,
                title = channel.name,
                imageUrl = channel.logoUrl,
                type = Type.CHANNEL,
                streamUrl = channel.streamUrl,
                lastWatched = channel.lastWatched,
                isFavorite = channel.isFavorite
            )
        }
        
        fun fromMovie(movie: Movie): MediaItem {
            return MediaItem(
                id = movie.id,
                title = movie.name,
                imageUrl = movie.posterUrl,
                type = Type.MOVIE,
                streamUrl = movie.streamUrl,
                lastWatched = movie.lastWatched,
                progress = movie.watchProgress,
                isFavorite = movie.isFavorite
            )
        }
        
        fun fromSeries(series: Series): MediaItem {
            return MediaItem(
                id = series.id,
                title = series.name,
                imageUrl = series.posterUrl,
                type = Type.SERIES,
                lastWatched = series.lastWatched,
                isFavorite = series.isFavorite
            )
        }
    }
}
