<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Streamr" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/accent_green</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_light_green</item>
        <item name="colorSecondaryVariant">@color/primary_light</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="backgroundColor">@color/background_primary</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    
    <style name="Theme.Streamr.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <style name="Theme.Streamr.FullScreen" parent="Theme.Streamr.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    
    <!-- Custom Button Styles -->
    <style name="StreamrButton" parent="Widget.MaterialComponents.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">16dp</item>
        <item name="cornerRadius">24dp</item>
    </style>
    
    <style name="StreamrButton.Primary">
        <item name="backgroundTint">@color/accent_green</item>
    </style>
    
    <style name="StreamrButton.Secondary">
        <item name="backgroundTint">@color/primary_light</item>
    </style>
    
    <!-- EditText Styles -->
    <style name="StreamrEditText" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:background">@drawable/edittext_background</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
    </style>
    
    <!-- Card Styles -->
    <style name="StreamrCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/background_card</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    
    <!-- Text Styles -->
    <style name="StreamrText">
        <item name="android:textColor">@color/text_primary</item>
    </style>
    
    <style name="StreamrText.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="StreamrText.Subtitle">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
    </style>
    
    <style name="StreamrText.Body">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
</resources>
