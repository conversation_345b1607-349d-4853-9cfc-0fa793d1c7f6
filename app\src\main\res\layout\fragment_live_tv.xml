<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:orientation="vertical">

    <!-- Tab Layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <LinearLayout
                android:id="@+id/tabCategories"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="16dp">

                <TextView
                    android:id="@+id/textCategories"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Categories"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:id="@+id/indicatorCategories"
                    android:layout_width="match_parent"
                    android:layout_height="3dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/accent_green" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tabChannels"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="16dp">

                <TextView
                    android:id="@+id/textChannels"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Channels"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:id="@+id/indicatorChannels"
                    android:layout_width="match_parent"
                    android:layout_height="3dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/transparent" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tabRecordings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="16dp">

                <TextView
                    android:id="@+id/textRecordings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Recordings"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:id="@+id/indicatorRecordings"
                    android:layout_width="match_parent"
                    android:layout_height="3dp"
                    android:layout_marginTop="13dp"
                    android:background="@color/transparent" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/primary_light" />

    </LinearLayout>

    <!-- Content Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Categories View -->
        <ScrollView
            android:id="@+id/categoriesView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/categoryItem1"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Sports"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem2"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="News"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem3"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Entertainment"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem4"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Movies"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem5"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Kids"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem6"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Music"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem7"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Lifestyle"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/categoryItem8"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Documentaries"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <!-- Channels View -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvChannels"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="16dp"
            android:visibility="gone" />

        <!-- Recordings View -->
        <LinearLayout
            android:id="@+id/recordingsView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:alpha="0.6"
                android:src="@drawable/ic_record"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="No Recordings"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Recording feature coming soon"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:alpha="0.6"
                android:src="@drawable/ic_tv"
                android:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:text="No Live Channels"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Add a playlist to see live channels"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
