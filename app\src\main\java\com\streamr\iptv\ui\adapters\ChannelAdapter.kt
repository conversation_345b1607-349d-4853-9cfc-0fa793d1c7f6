package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.streamr.iptv.R
import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.databinding.ItemChannelBinding

class ChannelAdapter(
    private val onItemClick: (Channel) -> Unit
) : ListAdapter<Channel, ChannelAdapter.ChannelViewHolder>(ChannelDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChannelViewHolder {
        val binding = ItemChannelBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ChannelViewHolder(binding, onItemClick)
    }
    
    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class ChannelViewHolder(
        private val binding: ItemChannelBinding,
        private val onItemClick: (Channel) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(channel: Channel) {
            binding.tvChannelName.text = channel.name
            binding.tvChannelGroup.text = channel.groupTitle ?: "General"
            
            // Load channel logo
            Glide.with(binding.root.context)
                .load(channel.logoUrl)
                .placeholder(R.drawable.placeholder_channel)
                .error(R.drawable.placeholder_channel)
                .into(binding.ivChannelLogo)
            
            // Set click listener
            binding.root.setOnClickListener {
                onItemClick(channel)
            }
        }
    }
    
    private class ChannelDiffCallback : DiffUtil.ItemCallback<Channel>() {
        override fun areItemsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem == newItem
        }
    }
}
