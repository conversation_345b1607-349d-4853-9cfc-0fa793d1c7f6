package com.streamr.iptv

import android.app.Application
import com.streamr.iptv.data.database.StreamrDatabase
import com.streamr.iptv.data.repository.StreamrRepository

class StreamrApplication : Application() {
    
    val database by lazy { StreamrDatabase.getDatabase(this) }
    val repository by lazy { StreamrRepository(database) }
    
    override fun onCreate() {
        super.onCreate()
        instance = this
    }
    
    companion object {
        lateinit var instance: StreamrApplication
            private set
    }
}
