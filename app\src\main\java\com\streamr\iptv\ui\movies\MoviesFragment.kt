package com.streamr.iptv.ui.movies

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.streamr.iptv.databinding.FragmentMoviesBinding
import com.streamr.iptv.ui.adapters.MediaAdapter

class MoviesFragment : Fragment() {
    
    private var _binding: FragmentMoviesBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: MoviesViewModel by viewModels()
    private lateinit var moviesAdapter: MediaAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMoviesBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        moviesAdapter = MediaAdapter { movie ->
            viewModel.playMovie(movie)
        }
        
        binding.rvMovies.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = moviesAdapter
        }
    }
    
    private fun observeViewModel() {
        viewModel.movies.observe(viewLifecycleOwner) { movies ->
            moviesAdapter.submitList(movies)
            binding.emptyState.visibility = if (movies.isEmpty()) View.VISIBLE else View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
