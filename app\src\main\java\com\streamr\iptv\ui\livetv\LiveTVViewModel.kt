package com.streamr.iptv.ui.livetv

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.data.repository.StreamrRepository

class LiveTVViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val channels: LiveData<List<Channel>> = MediatorLiveData<List<Channel>>().apply {
        // Get all channels from all active playlists
        val playlists = repository.getActivePlaylists()
        addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                // For now, get channels from the first playlist
                // In a real app, you'd combine channels from all playlists
                val firstPlaylist = playlistList.first()
                removeSource(playlists)
                addSource(repository.getChannelsByPlaylist(firstPlaylist.id)) { channelList ->
                    value = channelList ?: emptyList()
                }
            } else {
                value = emptyList()
            }
        }
    }
    
    fun playChannel(channel: Channel) {
        // TODO: Implement channel playback
        // This would typically:
        // 1. Update watch history
        // 2. Start PlayerActivity with the channel
    }
}
