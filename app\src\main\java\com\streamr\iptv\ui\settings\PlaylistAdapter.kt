package com.streamr.iptv.ui.settings

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.streamr.iptv.data.model.Playlist
import com.streamr.iptv.databinding.ItemPlaylistBinding
import java.text.SimpleDateFormat
import java.util.*

class PlaylistAdapter(
    private val onEditClick: (Playlist) -> Unit,
    private val onDeleteClick: (Playlist) -> Unit
) : ListAdapter<Playlist, PlaylistAdapter.PlaylistViewHolder>(PlaylistDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PlaylistViewHolder {
        val binding = ItemPlaylistBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return PlaylistViewHolder(binding, onEditClick, onDeleteClick)
    }
    
    override fun onBindViewHolder(holder: PlaylistViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class PlaylistViewHolder(
        private val binding: ItemPlaylistBinding,
        private val onEditClick: (Playlist) -> Unit,
        private val onDeleteClick: (Playlist) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        
        private val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        
        fun bind(playlist: Playlist) {
            binding.tvPlaylistName.text = playlist.name
            binding.tvPlaylistType.text = playlist.type.name.replace("_", " ")
            binding.tvPlaylistUrl.text = playlist.serverUrl
            binding.tvLastUsed.text = "Last used: ${dateFormat.format(Date(playlist.lastUsed))}"
            
            binding.btnEdit.setOnClickListener {
                onEditClick(playlist)
            }
            
            binding.btnDelete.setOnClickListener {
                onDeleteClick(playlist)
            }
        }
    }
    
    private class PlaylistDiffCallback : DiffUtil.ItemCallback<Playlist>() {
        override fun areItemsTheSame(oldItem: Playlist, newItem: Playlist): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Playlist, newItem: Playlist): Boolean {
            return oldItem == newItem
        }
    }
}
