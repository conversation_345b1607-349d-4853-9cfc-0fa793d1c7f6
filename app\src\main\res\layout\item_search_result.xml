<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal"
    android:padding="16dp">

    <androidx.cardview.widget.CardView
        android:layout_width="80dp"
        android:layout_height="60dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivThumbnail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_card"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/ivTypeIcon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="bottom|start"
                android:layout_margin="4dp"
                android:src="@drawable/ic_tv"
                app:tint="@color/white" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Media Title"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="normal" />

            <ImageView
                android:id="@+id/ivFavorite"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="8dp"
                android:src="@drawable/ic_favorite"
                android:visibility="gone"
                app:tint="@color/accent_green" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/live_indicator_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="LIVE"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
