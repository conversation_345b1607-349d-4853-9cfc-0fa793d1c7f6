package com.streamr.iptv.ui.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.Playlist
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.launch

class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val playlists: LiveData<List<Playlist>> = repository.getAllPlaylists()
    
    fun deletePlaylist(playlist: Playlist) {
        viewModelScope.launch {
            repository.deletePlaylist(playlist)
        }
    }
}
