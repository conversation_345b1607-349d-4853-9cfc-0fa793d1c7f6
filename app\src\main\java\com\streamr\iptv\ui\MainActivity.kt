package com.streamr.iptv.ui

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.streamr.iptv.R
import com.streamr.iptv.databinding.ActivityMainBinding
import com.streamr.iptv.ui.home.HomeFragment
import com.streamr.iptv.ui.livetv.LiveTVFragment
import com.streamr.iptv.ui.movies.MoviesFragment
import com.streamr.iptv.ui.series.SeriesFragment
import com.streamr.iptv.ui.settings.SettingsFragment
import com.streamr.iptv.ui.search.SearchActivity

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private var currentTab = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupBottomNavigation()
        
        // Load home fragment by default
        if (savedInstanceState == null) {
            loadFragment(HomeFragment(), 0)
        }
    }
    
    private fun setupBottomNavigation() {
        binding.btnSearch.setOnClickListener {
            startActivity(Intent(this, SearchActivity::class.java))
        }
        
        binding.navHome.setOnClickListener {
            if (currentTab != 0) {
                loadFragment(HomeFragment(), 0)
                updateBottomNavigation(0)
            }
        }
        
        binding.navLiveTV.setOnClickListener {
            if (currentTab != 1) {
                loadFragment(LiveTVFragment(), 1)
                updateBottomNavigation(1)
            }
        }
        
        binding.navMovies.setOnClickListener {
            if (currentTab != 2) {
                loadFragment(MoviesFragment(), 2)
                updateBottomNavigation(2)
            }
        }
        
        binding.navSeries.setOnClickListener {
            if (currentTab != 3) {
                loadFragment(SeriesFragment(), 3)
                updateBottomNavigation(3)
            }
        }
        
        binding.navSettings.setOnClickListener {
            if (currentTab != 4) {
                loadFragment(SettingsFragment(), 4)
                updateBottomNavigation(4)
            }
        }
    }
    
    private fun loadFragment(fragment: Fragment, tabIndex: Int) {
        currentTab = tabIndex
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, fragment)
            .commit()
    }
    
    private fun updateBottomNavigation(selectedTab: Int) {
        // Reset all tabs to inactive state
        resetBottomNavigation()
        
        // Set selected tab to active state
        when (selectedTab) {
            0 -> {
                binding.iconHome.setColorFilter(ContextCompat.getColor(this, R.color.text_primary))
                binding.textHome.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
                binding.textHome.setTypeface(null, android.graphics.Typeface.BOLD)
            }
            1 -> {
                binding.iconLiveTV.setColorFilter(ContextCompat.getColor(this, R.color.text_primary))
                binding.textLiveTV.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
                binding.textLiveTV.setTypeface(null, android.graphics.Typeface.BOLD)
            }
            2 -> {
                binding.iconMovies.setColorFilter(ContextCompat.getColor(this, R.color.text_primary))
                binding.textMovies.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
                binding.textMovies.setTypeface(null, android.graphics.Typeface.BOLD)
            }
            3 -> {
                binding.iconSeries.setColorFilter(ContextCompat.getColor(this, R.color.text_primary))
                binding.textSeries.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
                binding.textSeries.setTypeface(null, android.graphics.Typeface.BOLD)
            }
            4 -> {
                binding.iconSettings.setColorFilter(ContextCompat.getColor(this, R.color.text_primary))
                binding.textSettings.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
                binding.textSettings.setTypeface(null, android.graphics.Typeface.BOLD)
            }
        }
    }
    
    private fun resetBottomNavigation() {
        val secondaryColor = ContextCompat.getColor(this, R.color.text_secondary)
        
        binding.iconHome.setColorFilter(secondaryColor)
        binding.textHome.setTextColor(secondaryColor)
        binding.textHome.setTypeface(null, android.graphics.Typeface.NORMAL)
        
        binding.iconLiveTV.setColorFilter(secondaryColor)
        binding.textLiveTV.setTextColor(secondaryColor)
        binding.textLiveTV.setTypeface(null, android.graphics.Typeface.NORMAL)
        
        binding.iconMovies.setColorFilter(secondaryColor)
        binding.textMovies.setTextColor(secondaryColor)
        binding.textMovies.setTypeface(null, android.graphics.Typeface.NORMAL)
        
        binding.iconSeries.setColorFilter(secondaryColor)
        binding.textSeries.setTextColor(secondaryColor)
        binding.textSeries.setTypeface(null, android.graphics.Typeface.NORMAL)
        
        binding.iconSettings.setColorFilter(secondaryColor)
        binding.textSettings.setTextColor(secondaryColor)
        binding.textSettings.setTypeface(null, android.graphics.Typeface.NORMAL)
    }
}
