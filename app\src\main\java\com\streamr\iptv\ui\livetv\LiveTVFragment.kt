package com.streamr.iptv.ui.livetv

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.streamr.iptv.databinding.FragmentLiveTvBinding
import com.streamr.iptv.ui.adapters.ChannelAdapter

class LiveTVFragment : Fragment() {
    
    private var _binding: FragmentLiveTvBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: LiveTVViewModel by viewModels()
    private lateinit var channelAdapter: ChannelAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLiveTvBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        channelAdapter = ChannelAdapter { channel ->
            viewModel.playChannel(channel)
        }
        
        binding.rvChannels.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = channelAdapter
        }
    }
    
    private fun observeViewModel() {
        viewModel.channels.observe(viewLifecycleOwner) { channels ->
            channelAdapter.submitList(channels)
            binding.emptyState.visibility = if (channels.isEmpty()) View.VISIBLE else View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
