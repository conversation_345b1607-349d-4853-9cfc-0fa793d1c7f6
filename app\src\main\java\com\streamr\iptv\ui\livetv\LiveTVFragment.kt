package com.streamr.iptv.ui.livetv

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.streamr.iptv.R
import com.streamr.iptv.data.model.ChannelCategory
import com.streamr.iptv.databinding.FragmentLiveTvBinding
import com.streamr.iptv.ui.adapters.ChannelAdapter

class LiveTVFragment : Fragment() {

    private var _binding: FragmentLiveTvBinding? = null
    private val binding get() = _binding!!

    private val viewModel: LiveTVViewModel by viewModels()
    private lateinit var channelAdapter: ChannelAdapter
    private var currentTab = 0 // 0: Categories, 1: Channels, 2: Recordings

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLiveTvBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupTabs()
        setupRecyclerView()
        setupCategoryClickListeners()
        observeViewModel()
    }

    private fun setupTabs() {
        binding.tabCategories.setOnClickListener {
            if (currentTab != 0) {
                switchTab(0)
            }
        }

        binding.tabChannels.setOnClickListener {
            if (currentTab != 1) {
                switchTab(1)
            }
        }

        binding.tabRecordings.setOnClickListener {
            if (currentTab != 2) {
                switchTab(2)
            }
        }
    }

    private fun switchTab(tabIndex: Int) {
        currentTab = tabIndex

        // Reset all tabs
        resetTabs()

        // Hide all views
        binding.categoriesView.visibility = View.GONE
        binding.rvChannels.visibility = View.GONE
        binding.recordingsView.visibility = View.GONE

        // Show selected tab and view
        when (tabIndex) {
            0 -> {
                binding.textCategories.setTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
                binding.indicatorCategories.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.accent_green))
                binding.categoriesView.visibility = View.VISIBLE
            }
            1 -> {
                binding.textChannels.setTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
                binding.indicatorChannels.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.accent_green))
                binding.rvChannels.visibility = View.VISIBLE
            }
            2 -> {
                binding.textRecordings.setTextColor(ContextCompat.getColor(requireContext(), R.color.text_primary))
                binding.indicatorRecordings.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.accent_green))
                binding.recordingsView.visibility = View.VISIBLE
            }
        }
    }

    private fun resetTabs() {
        val secondaryColor = ContextCompat.getColor(requireContext(), R.color.text_secondary)
        val transparentColor = ContextCompat.getColor(requireContext(), R.color.transparent)

        binding.textCategories.setTextColor(secondaryColor)
        binding.textChannels.setTextColor(secondaryColor)
        binding.textRecordings.setTextColor(secondaryColor)

        binding.indicatorCategories.setBackgroundColor(transparentColor)
        binding.indicatorChannels.setBackgroundColor(transparentColor)
        binding.indicatorRecordings.setBackgroundColor(transparentColor)
    }

    private fun setupRecyclerView() {
        channelAdapter = ChannelAdapter { channel ->
            viewModel.playChannel(channel)
        }

        binding.rvChannels.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = channelAdapter
        }
    }

    private fun setupCategoryClickListeners() {
        binding.categoryItem1.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.SPORTS) }
        binding.categoryItem2.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.NEWS) }
        binding.categoryItem3.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.ENTERTAINMENT) }
        binding.categoryItem4.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.MOVIES) }
        binding.categoryItem5.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.KIDS) }
        binding.categoryItem6.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.MUSIC) }
        binding.categoryItem7.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.OTHER) }
        binding.categoryItem8.setOnClickListener { viewModel.loadChannelsByCategory(ChannelCategory.DOCUMENTARY) }
    }

    private fun observeViewModel() {
        viewModel.channels.observe(viewLifecycleOwner) { channels ->
            channelAdapter.submitList(channels)
            binding.emptyState.visibility = if (channels.isEmpty() && currentTab == 1) View.VISIBLE else View.GONE
        }

        viewModel.categorySelected.observe(viewLifecycleOwner) { categorySelected ->
            if (categorySelected) {
                switchTab(1) // Switch to channels tab when category is selected
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
