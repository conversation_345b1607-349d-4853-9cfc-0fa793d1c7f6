package com.streamr.iptv.ui.series

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem

class SeriesViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val series: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val playlists = repository.getActivePlaylists()
        addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                val firstPlaylist = playlistList.first()
                removeSource(playlists)
                addSource(repository.getSeriesByPlaylist(firstPlaylist.id)) { seriesList ->
                    value = seriesList?.map { MediaItem.fromSeries(it) } ?: emptyList()
                }
            } else {
                value = emptyList()
            }
        }
    }
    
    fun showSeries(seriesItem: MediaItem) {
        // TODO: Implement series detail view
        // This would typically:
        // 1. Show series details with episodes
        // 2. Navigate to series detail activity/fragment
    }
}
