package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.streamr.iptv.R
import com.streamr.iptv.databinding.ItemSearchResultBinding

class SearchAdapter(
    private val onItemClick: (MediaItem) -> Unit
) : ListAdapter<MediaItem, SearchAdapter.SearchViewHolder>(MediaDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchViewHolder {
        val binding = ItemSearchResultBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SearchViewHolder(binding, onItemClick)
    }
    
    override fun onBindViewHolder(holder: SearchViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class SearchViewHolder(
        private val binding: ItemSearchResultBinding,
        private val onItemClick: (MediaItem) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: MediaItem) {
            binding.tvTitle.text = item.title
            
            // Set type indicator
            when (item.type) {
                MediaItem.Type.CHANNEL -> {
                    binding.tvType.text = "LIVE"
                    binding.tvType.setBackgroundResource(R.drawable.live_indicator_background)
                    binding.ivTypeIcon.setImageResource(R.drawable.ic_tv)
                }
                MediaItem.Type.MOVIE -> {
                    binding.tvType.text = "MOVIE"
                    binding.tvType.setBackgroundResource(R.drawable.movie_indicator_background)
                    binding.ivTypeIcon.setImageResource(R.drawable.ic_movie)
                }
                MediaItem.Type.SERIES -> {
                    binding.tvType.text = "SERIES"
                    binding.tvType.setBackgroundResource(R.drawable.series_indicator_background)
                    binding.ivTypeIcon.setImageResource(R.drawable.ic_tv)
                }
            }
            
            // Load thumbnail
            Glide.with(binding.root.context)
                .load(item.imageUrl)
                .placeholder(R.drawable.placeholder_media)
                .error(R.drawable.placeholder_media)
                .into(binding.ivThumbnail)
            
            // Show/hide favorite icon
            binding.ivFavorite.visibility = if (item.isFavorite) View.VISIBLE else View.GONE
            
            // Set click listener
            binding.root.setOnClickListener {
                onItemClick(item)
            }
        }
    }
    
    private class MediaDiffCallback : DiffUtil.ItemCallback<MediaItem>() {
        override fun areItemsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem.id == newItem.id && oldItem.type == newItem.type
        }
        
        override fun areContentsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem == newItem
        }
    }
}
